from DrissionPage import ChromiumOptions, ChromiumPage
import os
import sys
import random
import time
import logging
from dotenv import load_dotenv

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

# 加载环境变量
load_dotenv()

# 用户代理列表
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36",
]

def get_user_agent():
    """获取随机用户代理"""
    # 如果设置了自定义用户代理，则使用自定义的
    custom_agent = os.getenv("CUSTOM_USER_AGENT")
    if custom_agent:
        return custom_agent
    # 否则随机选择一个用户代理
    return random.choice(USER_AGENTS)

def init_browser():
    """初始化并打开浏览器"""
    try:
        logging.info("正在初始化浏览器...")

        # 获取Chrome选项
        co = ChromiumOptions()

        # 设置用户代理
        user_agent = get_user_agent()
        co.set_user_agent(user_agent)
        logging.info(f"使用用户代理: {user_agent}")

        # 设置各种浏览器参数
        co.set_pref("credentials_enable_service", False)
        co.set_argument("--hide-crash-restore-bubble")

        # 加载扩展插件 - 使用绝对路径
        extension_path = os.path.abspath(os.path.join(os.getcwd(), "自动绑卡插件"))
        if os.path.exists(extension_path):
            # 使用 --load-extension 参数加载扩展
            co.set_argument(f"--load-extension={extension_path}")
            logging.info(f"已加载扩展插件: {extension_path}")
        else:
            logging.warning(f"扩展插件路径不存在: {extension_path}")

        # 禁用扩展安全检查和相关限制
        co.set_argument("--disable-extensions-file-access-check")
        co.set_argument("--disable-extensions-http-throttling")
        co.set_argument("--allow-running-insecure-content")
        co.set_argument("--disable-web-security")
        co.set_argument("--disable-features=VizDisplayCompositor")
        # 允许加载未打包的扩展
        co.set_argument("--enable-extensions")
        # 强制启用开发者模式高亮
        co.set_argument("--force-dev-mode-highlighting")
        # 禁用扩展的开发者模式警告
        co.set_argument("--disable-dev-shm-usage")
        # 禁用默认浏览器检查
        co.set_argument("--no-default-browser-check")
        # 禁用首次运行体验
        co.set_argument("--no-first-run")
        # 允许过时的插件
        co.set_argument("--allow-outdated-plugins")
        # 禁用插件发现
        co.set_argument("--disable-plugins-discovery")
        # 禁用扩展更新
        co.set_argument("--disable-background-timer-throttling")
        co.set_argument("--disable-renderer-backgrounding")
        co.set_argument("--disable-backgrounding-occluded-windows")

        # 自动选择端口
        co.auto_port()

        # 设置无头模式
        headless = os.getenv("BROWSER_HEADLESS", "False").lower() == "False"
        co.headless(headless)
        if headless:
            logging.info("启用无头模式")
        else:
            logging.info("启用有头模式")

        # 针对Mac系统进行特殊设置
        if sys.platform == "darwin":
            co.set_argument("--no-sandbox")
            co.set_argument("--disable-gpu")
            logging.info("检测到Mac系统，添加特殊参数")

        # 创建浏览器实例 - 使用ChromiumPage而不是Chromium
        browser = ChromiumPage(co)
        logging.info("浏览器初始化成功!")

        return browser
        
    except Exception as e:
        logging.error(f"浏览器初始化失败: {str(e)}")
        raise

def open_url(browser, url):
    """打开指定URL"""
    try:
        logging.info(f"正在打开URL: {url}")
        browser.get(url)
        logging.info(f"成功打开URL: {url}")
        
        # 获取当前页面标题
        title = browser.title
        logging.info(f"页面标题: {title}")
        
        return True
    except Exception as e:
        logging.error(f"打开URL失败: {str(e)}")
        return False

def quit_browser(browser):
    """安全关闭浏览器"""
    if browser:
        try:
            logging.info("正在关闭浏览器...")
            browser.quit()
            logging.info("浏览器已关闭")
        except Exception as e:
            logging.error(f"关闭浏览器失败: {str(e)}")

def main():
    """主函数"""
    try:
        # 初始化浏览器
        browser = init_browser()
        
        # 打开网页
        url = os.getenv("TARGET_URL", "https://www.google.com")
        open_url(browser, url)
        
        # 等待一段时间（可以从环境变量中获取，默认30秒）
        wait_time = int(os.getenv("WAIT_TIME", "30"))
        logging.info(f"浏览器将保持打开状态 {wait_time} 秒...")
        
        # 倒计时
        for i in range(wait_time, 0, -1):
            if i % 5 == 0:  # 每5秒打印一次
                logging.info(f"剩余时间: {i} 秒")
            time.sleep(1)
        
    except KeyboardInterrupt:
        logging.info("检测到键盘中断，正在退出...")
    except Exception as e:
        logging.error(f"运行过程中发生错误: {str(e)}")
        import traceback
        logging.error(traceback.format_exc())
    finally:
        # 关闭浏览器
        if 'browser' in locals():
            quit_browser(browser)
        logging.info("程序已退出")

if __name__ == "__main__":
    main() 